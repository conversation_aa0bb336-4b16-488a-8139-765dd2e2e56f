<section class="zone">
  <!-- <v-layout justify-end>
    <v-btn
      small
      outline
      @click="generateNewZoneRate"
      color="warning"
      class="mb-2"
      :disabled="!isEdited"
    >
      <v-icon small class="mr-2">add</v-icon>Add zone rates
    </v-btn>
  </v-layout> -->

  <v-data-table
    class="default-table-dark gd-dark-theme connected-rows-table"
    :headers="headers"
    :items="getGroupedZoneRateTableData()"
    :rows-per-page-items="[10, 20]"
    hide-actions
  >
    <template v-slot:items="props">
      <tr
        :class="{
          'service-header-row': props.item.isServiceHeader,
          'zone-child-row': props.item.isRangeRow,
          'form-container': !props.item.isServiceHeader,
          'item-selected': editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index,
          'empty-rate-item':  isRateItemEmpty(props.item),
        }"
        style="cursor: pointer; position: relative"
        @click="props.item.isRangeRow && isEdited ? editZone(props.item.serviceTypeId, props.item.index) :  cancelZoneEdit();"
      >
        <td class="extra-wide">
          <v-layout md12 align-center justify-space-between>
            <v-flex v-if="props.item.isServiceHeader">
              <span class="service-name">
                {{ serviceTypeName(props.item.serviceTypeId) }}
              </span>
            </v-flex>
            <v-flex v-else>
              <v-text-field
                v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
                class="small-input"
                box
                hide-details
                v-model="zoneRate.zoneName"
                type="text"
                :disabled="!isEdited"
                :rules="[validate.required]"
                @focus="$event.target.select()"
              >
              </v-text-field>
              <span v-else class="subheading grey--text zone-child-text">
                {{ props.item.serviceTypeName }}
              </span>
            </v-flex>
          </v-layout>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
              class="small-input"
              box
              hide-details
              v-model.number="zoneRate.rate"
              type="number"
              :prefix="'$'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.zoneRate }}
            </span>
          </v-flex>
        </td>

        <td v-else class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
              class="small-input"
              box
              type="number"
              v-model.number="zoneRate.percentage"
              hide-details
              :suffix="'%'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.calculation }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
              class="small-input"
              box
              type="number"
              v-model.number="zoneRate.additionalPickUpFlagFall"
              hide-details
              :disabled="!isEdited"
              :prefix="'$'"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.pickupFlagfall }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
              class="small-input"
              box
              type="number"
              v-model.number="zoneRate.additionalDropOffFlagFall"
              hide-details
              :prefix="'$'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.dropoffFlagfall }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-flex>
            <v-select
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
              :items="applicableFuelSurcharges"
              item-text="shortName"
              item-value="id"
              v-model="zoneRate.appliedFuelSurchargeId"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              box
              hide-details
              class="small-input"
            >
            </v-select>
            <span v-else class="subheading grey--text">
              {{ props.item.appliedFuelSurcharge }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-select
            v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
            box
            class="small-input"
            :items="applicableDemurrages"
            :disabled="!isEdited"
            item-text="shortName"
            hide-details
            item-value="id"
            v-model="zoneRate.demurrage.appliedDemurrageId"
            :rules="[validate.required, validate.number]"
          >
          </v-select>
          <span v-else class="subheading grey--text">
            {{ props.item.appliedDemurrageCharge }}
          </span>
        </td>

        <td>
          <v-flex>
            <v-layout
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
            >
              <v-text-field
                class="small-input"
                hide-details
                box
                :prefix="'$'"
                type="number"
                :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                v-model.number="zoneRate.demurrage.rate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-layout>
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageRate }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
              box
              class="small-input"
              hide-details
              :suffix="'mins'"
              type="number"
              v-model.number="graceTimeInMinutes"
              :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
              :rules="[validate.required, validate.number]"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageGrace }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-flex>
            <v-select
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index && !openDialogForEdit"
              v-model="zoneRate.demurrage.demurrageFuelSurchargeApplies"
              :items="[
                { text: 'Apply', value: true },
                { text: 'Don\'t Apply', value: false }
              ]"
              :disabled="!isEdited"
              item-text="text"
              item-value="value"
              class="small-input"
              hide-details
              box
            />
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageFuelSurchargeApplies }}
            </span>
          </v-flex>
        </td>
        <td class="text-xs-center" v-if="!props.item.isServiceHeader">
          <!-- <v-btn
            v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
            small
            flat
            icon
            @click="cancelZoneEdit()"
          >
            <v-icon color="success"> save </v-icon>
          </v-btn> -->
          <v-btn
            v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
            small
            flat
            icon
            @click="removeRate()"
          >
            <v-icon color="error"> delete </v-icon>
          </v-btn>
        </td>

        <td v-if="props.item.isServiceHeader" class="copy-controls">
          <div>
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-if="!copyActive"
                  icon
                  flat
                  @click="generateNewZoneRate(props.item.serviceTypeId, props.item.index)"
                  color="warning"
                  class="mr-1"
                  :disabled="!isEdited"
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-icon size="18">fas fa-plus-circle</v-icon>
                </v-btn>
              </template>
              <span>Add new zone rate</span>
            </v-tooltip>

            <v-btn
              v-if="!copyActive"
              icon
              flat
              @click="copyValue(props.item.serviceTypeId)"
              :disabled="!isEdited"
              v-bind="attrs"
              v-on="on"
            >
              <v-icon size="18"color="accent">
                fad fa-copy
              </v-icon>
            </v-btn>

            <v-icon
              v-if="copyActive && copiedServiceTypeId === props.item.serviceTypeId"
              fab
              @click="bulkChanges()"
              color="yellow"
            >
              fad fa-paste
            </v-icon>

            <v-checkbox
              v-if="props.item.isServiceHeader && copyActive && copiedServiceTypeId !== props.item.serviceTypeId"
              v-model="selectedBulkChanges"
              :value="props.item.serviceTypeId"
              hide-details
              small
              class="ma-0"
            ></v-checkbox>
          </div>
        </td>
      </tr>
    </template>
  </v-data-table>

  <!-- DIALOG -->
  <v-dialog
    v-if="zoneRate && openDialogForEdit"
    v-model="dialogIsOpen"
    content-class="v-dialog-custom"
    width="700px"
    persistent
    no-click-animation
  >
    <v-flex md12>
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Zone Rate</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="cancelZoneEdit"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout row wrap class="app-theme__center-content--body dialog-content">
        <v-flex md12 class="body-scrollable--70" pa-3>
          <v-layout v-if="isFleetAsset" align-center class="px-2 pt-2">
            <v-flex md12>
              <v-alert
                :value="true"
                type="info"
                outline
                style="background-color: #26263a93"
              >
                <p class="ma-0 alert-text">
                  Please note that a clients fuel surcharge rate above 0% for
                  any zone will satisfy a fuel surcharge application of "Apply
                  (Client > 0%)".
                </p>
              </v-alert>
            </v-flex>
          </v-layout>
          <v-layout align-center class="pa-2">
            <h5 class="subheader--bold pr-3">Primary rate</h5>
            <v-flex>
              <v-divider></v-divider>
            </v-flex>
          </v-layout>
          <v-form ref="zoneForm">
            <v-layout class="px-2" column>
              <v-layout wrap v-if="!isFleetAsset">
                <v-flex md12>
                  <v-select
                    label="Service Type"
                    hint="Service Type"
                    :rules="[validate.required]"
                    v-if="isNewRate"
                    v-model="selectedServiceTypeId"
                    :items="existingServiceTypes"
                    item-value="serviceTypeId"
                    item-text="optionSelectName"
                    solo
                    flat
                    class="v-solo-custom mb-2"
                    persistent-hint
                  >
                  </v-select>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    label="Zone Name"
                    hint="Zone Name"
                    v-model="zoneRate.zoneName"
                    type="text"
                    :disabled="!isEdited"
                    :rules="[validate.required]"
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    label="Zone Rate"
                    hint="Zone Rate"
                    type="number"
                    :prefix="'$'"
                    :disabled="!isEdited"
                    v-model.number="zoneRate.rate"
                    :rules="[validate.required, validate.number]"
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                  >
                  </v-text-field>
                </v-flex>

                <v-flex md12>
                  <v-text-field
                    label="Pickup Flagfall"
                    hint="Pickup Flagfall"
                    :prefix="'$'"
                    v-model.number="zoneRate.additionalPickUpFlagFall"
                    type="number"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.number]"
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    label="Dropoff Flagfall"
                    hint="Dropoff Flagfall"
                    :prefix="'$'"
                    type="number"
                    :disabled="!isEdited"
                    v-model.number="zoneRate.additionalDropOffFlagFall"
                    :rules="[validate.required, validate.number]"
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                  >
                  </v-text-field>
                </v-flex>
              </v-layout>

              <v-layout v-if="isFleetAsset">
                <v-flex md12>
                  <v-text-field
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                    :label="'Percentage'"
                    hint="'Percentage'"
                    type="number"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.percentage, validate.nonNegative]"
                    v-model.number="zoneRate.percentage"
                  >
                  </v-text-field>
                </v-flex>
              </v-layout>
              <v-layout wrap>
                <v-flex md12>
                  <v-select
                    label="Fuel Surcharge Application"
                    hint="Fuel Surcharge Application"
                    :items="applicableFuelSurcharges"
                    item-text="shortName"
                    item-value="id"
                    v-model="zoneRate.appliedFuelSurchargeId"
                    :disabled="!isEdited"
                    :rules="[validate.required, validate.number]"
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                  >
                  </v-select>
                </v-flex>
                <v-flex md12 ma-2>
                  <v-layout align-center>
                    <h5 class="subheader--bold pr-3">Demurrage</h5>
                    <v-flex>
                      <v-divider></v-divider>
                    </v-flex>
                  </v-layout>
                </v-flex>
                <v-flex md12>
                  <v-select
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                    :items="applicableDemurrages"
                    :disabled="!isEdited"
                    item-text="shortName"
                    label="Demurrage Rate Application"
                    hint="Demurrage Rate Application"
                    item-value="id"
                    v-model="zoneRate.demurrage.appliedDemurrageId"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-select>
                </v-flex>
                <v-flex md12>
                  <v-text-field
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                    label="Demurrage Rate (per hour)"
                    hint="Demurrage Rate (per hour)"
                    :prefix="'$'"
                    type="number"
                    :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                    v-model.number="zoneRate.demurrage.rate"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>
                <v-flex md12 v-if="!isFleetAsset">
                  <v-text-field
                    solo
                    flat
                    persistent-hint
                    class="v-solo-custom mb-2"
                    label="Demurrage Grace Time (mins)"
                    hint="Demurrage Grace Time (mins)"
                    type="number"
                    v-model.number="graceTimeInMinutes"
                    :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                    :rules="[validate.required, validate.number]"
                  >
                  </v-text-field>
                </v-flex>

                <v-flex md12>
                  <v-layout class="label-container" justify-end>
                    <h6 class="subheader--faded pr-2" style="padding-top: 2px">
                      Apply fuel surcharge to demurrage duration
                    </h6>
                    <div>
                      <v-switch
                        v-model="zoneRate.demurrage.demurrageFuelSurchargeApplies"
                        :disabled="!isEdited"
                        class="ma-0 pa-0"
                        color="warning"
                      >
                      </v-switch>
                    </div>
                  </v-layout>
                </v-flex>
              </v-layout>
            </v-layout>
          </v-form>
        </v-flex>
        <v-flex md12>
          <v-divider></v-divider>
          <v-layout justify-space-between ma-2>
            <ConfirmationDialog
              message="You are about to remove this zone rate. Do you wish to proceed?"
              buttonText="Remove"
              title="Zone Rate removal confirmation"
              @confirm="removeRate"
              :buttonDisabled="!isEdited"
              buttonColor="error"
              :isFlatButton="false"
              :isOutlineButton="true"
              :isSmallButton="false"
              :isDepressedButton="true"
              confirmationButtonText="Confirm and Remove"
              :dialogIsActive="true"
            ></ConfirmationDialog>
            <v-btn
              color="blue"
              :disabled="!isEdited"
              depressed
              @click="saveZoneRate"
            >
              <span v-if="isNewRate">Add Zone Rate</span>
              <span v-else>Update Zone Rate</span>
            </v-btn>
          </v-layout>
        </v-flex>
      </v-layout>
    </v-flex>
  </v-dialog>

  <!-- <v-dialog
    v-if="zoneRate"
    v-model="dialogIsOpen"
    content-class="v-dialog-custom"
    width="600px"
    persistent
  >
  
    <div class="app-theme__center-content--body dialog-content">
 
    </div>
  </v-dialog> -->
</section>
