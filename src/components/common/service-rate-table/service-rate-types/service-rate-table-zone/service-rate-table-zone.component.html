<section class="zone">
  <!-- <v-layout justify-end>
    <v-btn
      small
      outline
      @click="generateNewZoneRate"
      color="warning"
      class="mb-2"
      :disabled="!isEdited"
    >
      <v-icon small class="mr-2">add</v-icon>Add zone rates
    </v-btn>
  </v-layout> -->

  <v-data-table
    class="default-table-dark gd-dark-theme connected-rows-table"
    :headers="headers"
    :items="getGroupedZoneRateTableData()"
    :rows-per-page-items="[10, 20]"
    hide-actions
  >
    <template v-slot:items="props">
      <tr
        :class="{
          'service-header-row': props.item.isServiceHeader,
          'zone-child-row': props.item.isRangeRow,
          'form-container': !props.item.isServiceHeader,
          'item-selected': editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index,
          'empty-rate-item':  isRateItemEmpty(props.item),
        }"
        style="cursor: pointer; position: relative"
        @click="props.item.isRangeRow && isEdited ? editZone(props.item.serviceTypeId, props.item.index) :  null;"
      >
        <td class="extra-wide">
          <v-layout md12 align-center justify-space-between>
            <v-flex v-if="props.item.isServiceHeader">
              <span class="service-name">
                {{ serviceTypeName(props.item.serviceTypeId) }}
              </span>
            </v-flex>
            <v-flex v-else>
              <v-text-field
                v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
                class="small-input"
                box
                hide-details
                v-model="zoneRate.zoneName"
                type="text"
                :disabled="!isEdited"
                :rules="[validate.required]"
                @focus="$event.target.select()"
              >
              </v-text-field>
              <span v-else class="subheading grey--text zone-child-text">
                {{ props.item.serviceTypeName }}
              </span>
            </v-flex>
          </v-layout>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              hide-details
              v-model.number="zoneRate.rate"
              type="number"
              :prefix="'$'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.zoneRate }}
            </span>
          </v-flex>
        </td>

        <td v-else class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              type="number"
              v-model.number="zoneRate.percentage"
              hide-details
              :suffix="'%'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.calculation }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              type="number"
              v-model.number="zoneRate.additionalPickUpFlagFall"
              hide-details
              :disabled="!isEdited"
              :prefix="'$'"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.pickupFlagfall }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient" class="fixed-width">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              class="small-input"
              box
              type="number"
              v-model.number="zoneRate.additionalDropOffFlagFall"
              hide-details
              :prefix="'$'"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              @focus="$event.target.select()"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.dropoffFlagfall }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-flex>
            <v-select
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              :items="applicableFuelSurcharges"
              item-text="shortName"
              item-value="id"
              v-model="zoneRate.appliedFuelSurchargeId"
              :disabled="!isEdited"
              :rules="[validate.required, validate.number]"
              box
              hide-details
              class="small-input"
            >
            </v-select>
            <span v-else class="subheading grey--text">
              {{ props.item.appliedFuelSurcharge }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-select
            v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
            box
            class="small-input"
            :items="applicableDemurrages"
            :disabled="!isEdited"
            item-text="shortName"
            hide-details
            item-value="id"
            v-model="zoneRate.demurrage.appliedDemurrageId"
            :rules="[validate.required, validate.number]"
          >
          </v-select>
          <span v-else class="subheading grey--text">
            {{ props.item.appliedDemurrageCharge }}
          </span>
        </td>

        <td>
          <v-flex>
            <v-layout
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
            >
              <v-text-field
                class="small-input"
                hide-details
                box
                :prefix="'$'"
                type="number"
                :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
                v-model.number="zoneRate.demurrage.rate"
                :rules="[validate.required, validate.number]"
              >
              </v-text-field>
            </v-layout>
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageRate }}
            </span>
          </v-flex>
        </td>

        <td v-if="props.item.isClient">
          <v-flex>
            <v-text-field
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              box
              class="small-input"
              hide-details
              :suffix="'mins'"
              type="number"
              v-model.number="graceTimeInMinutes"
              :disabled="zoneRate.demurrage.appliedDemurrageId === 3 || !isEdited"
              :rules="[validate.required, validate.number]"
            >
            </v-text-field>
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageGrace }}
            </span>
          </v-flex>
        </td>

        <td class="extra-wide">
          <v-flex>
            <v-select
              v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index"
              v-model="zoneRate.demurrage.demurrageFuelSurchargeApplies"
              :items="[
                { text: 'Apply', value: true },
                { text: 'Don\'t Apply', value: false }
              ]"
              :disabled="!isEdited"
              item-text="text"
              item-value="value"
              class="small-input"
              hide-details
              box
            />
            <span v-else class="subheading grey--text">
              {{ props.item.demurrageFuelSurchargeApplies }}
            </span>
          </v-flex>
        </td>
        <td class="text-xs-center" v-if="!props.item.isServiceHeader">
          <div v-if="editedServiceTypeId === props.item.serviceTypeId && editedZoneIndex === props.item.index">
            <v-btn
              small
              flat
              icon
              @click="saveZoneRate()"
              :disabled="!isEdited"
            >
              <v-icon color="success">save</v-icon>
            </v-btn>
            <!-- <v-btn
              small
              flat
              icon
              @click="cancelZoneEdit()"
            >
              <v-icon color="grey">close</v-icon>
            </v-btn> -->
            <v-btn
              small
              flat
              icon
              @click="removeRate()"
              :disabled="!isEdited"
            >
              <v-icon color="error">delete</v-icon>
            </v-btn>
          </div>
        </td>

        <td v-if="props.item.isServiceHeader" class="copy-controls">
          <div>
            <v-tooltip top>
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  v-if="!copyActive"
                  icon
                  flat
                  @click="generateNewZoneRate(props.item.serviceTypeId)"
                  color="warning"
                  class="mr-1"
                  :disabled="!isEdited"
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-icon size="18">fas fa-plus-circle</v-icon>
                </v-btn>
              </template>
              <span>Add new zone rate</span>
            </v-tooltip>

            <v-btn
              v-if="!copyActive"
              icon
              flat
              @click="copyValue(props.item.serviceTypeId)"
              :disabled="!isEdited"
            >
              <v-icon size="18"color="accent">
                fad fa-copy
              </v-icon>
            </v-btn>

            <v-icon
              v-if="copyActive && copiedServiceTypeId === props.item.serviceTypeId"
              fab
              @click="bulkChanges()"
              color="yellow"
            >
              fad fa-paste
            </v-icon>

            <v-checkbox
              v-if="props.item.isServiceHeader && copyActive && copiedServiceTypeId !== props.item.serviceTypeId"
              v-model="selectedBulkChanges"
              :value="props.item.serviceTypeId"
              hide-details
              small
              class="ma-0"
            ></v-checkbox>
          </div>
        </td>
      </tr>
    </template>
    </v-data-table>




</section>
