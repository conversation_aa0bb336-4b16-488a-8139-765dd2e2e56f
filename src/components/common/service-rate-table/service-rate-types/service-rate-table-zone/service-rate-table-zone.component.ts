import { GroupedDataItem } from '@/components/admin/ClientDetails/components/client_details_rate_summary/ClientServiceRateSummaryTableData';
import ConfirmationDialog from '@/components/common/ui-elements/confirmation_dialog.vue';
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { requestUserConfirmation } from '@/helpers/NotificationHelpers/ConfirmationHelpers';
import {
  FORM_VALIDATION_FAILED_MESSAGE,
  GENERIC_ERROR_MESSAGE,
  showNotification,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { getServiceTypeById } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import rateMultipliers from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import ShortLongName from '@/interface-models/Generic/ShortLongName/ShortLongName';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import { VForm } from '@/interface-models/Generic/VForm/VForm';
import { Validation } from '@/interface-models/Generic/Validation';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import applicableDemurrages from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { nextTick } from 'vue';
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  components: {
    ConfirmationDialog,
  },
})
export default class ServiceRateTableZone extends Vue {
  @Prop() public serviceRate: ClientServiceRate | FleetAssetServiceRate;
  @Prop() public fuelSurchargeRate:
    | ClientFuelSurchargeRate
    | FleetAssetFuelSurchargeRate;
  @Prop({ default: false }) public isFleetAsset: boolean;
  @Prop({ default: true }) public isEdited: boolean;
  public rateId: number = 2;

  // Copy functionality properties
  public selectedBulkChanges: number[] = [];
  public copyActive: boolean = false;
  public copiedServiceTypeId: number = -1;
  public openDialogForEdit: boolean = false;

  public applicableDemurrages: ShortLongName[] = applicableDemurrages.filter(
    (x: ShortLongName) => {
      if (x.id !== 4 && x.id !== 2) {
        return true;
      }

      if (!this.isFleetAsset && x.id === 4) {
        return false;
      }

      if (this.isFleetAsset && x.id === 2) {
        return false;
      }

      return true;
    },
  );
  public applicableFuelSurcharges: ShortLongName[] =
    applicableFuelSurcharges.filter((x: ShortLongName) =>
      !this.isFleetAsset && x.id === 2 ? false : true,
    );

  public isNewRate: boolean = false;
  public editedServiceTypeId: number = -1;
  public editedZoneIndex: number = -1;
  public selectedServiceTypeId: number = -1;
  public zoneRate: ZoneRateType | null = null;

  public $refs!: {
    zoneForm: VForm;
  };

  get dialogIsOpen(): boolean {
    return this.zoneRate !== null && this.openDialogForEdit;
  }

  set dialogIsOpen(value: boolean) {
    if (!value) {
      this.zoneRate = null;
    }
  }
  get headers(): TableHeader[] {
    const headers: TableHeader[] = [
      {
        text: 'Service / Zone',
        align: 'left',
        value: 'serviceTypeName',
        sortable: false,
        width: !this.isFleetAsset ? '120px' : '65px',
      },
    ];
    if (!this.isFleetAsset) {
      headers.push(
        ...([
          // {
          //   text: 'Zone',
          //   align: 'left',
          //   value: 'zoneName',
          //   sortable: false,
          // },
          {
            text: 'Rate',
            align: 'left',
            value: 'zoneRate',
            sortable: false,
            width: '75px',
          },
          {
            text: 'Pickup Flagfall',
            align: 'left',
            value: 'pickupFlagfall',
            sortable: false,
            width: '75px',
          },
          {
            text: 'Dropoff Flagfall',
            align: 'left',
            value: 'dropoffFlagfall',
            sortable: false,
            width: '75px',
          },
        ] as TableHeader[]),
      );
    } else {
      headers.push(
        ...([
          {
            text: 'Percentage',
            align: 'left',
            value: 'percent',
            sortable: false,
            width: !this.isFleetAsset ? '75px' : '45px',
          },
        ] as TableHeader[]),
      );
    }

    headers.push(
      ...([
        {
          text: 'Fuel Surcharge',
          align: 'center',
          value: 'appliedFuelSurcharge',
          sortable: false,
          width: !this.isFleetAsset ? '120px' : '75px',
        },
        {
          text: 'Demurrage',
          align: !this.isFleetAsset ? 'center' : 'left',
          value: 'demurrageType',
          sortable: false,
          width: !this.isFleetAsset ? '120px' : '75px',
        },
        {
          text: 'Demurrage Rate',
          align: !this.isFleetAsset ? 'center' : 'left',
          value: 'demurrageRate',
          sortable: false,
          width: '75px',
        },
      ] as TableHeader[]),
    );

    if (!this.isFleetAsset) {
      headers.push(
        ...([
          {
            text: 'Demurrage Grace',
            align: 'left',
            value: 'demurrageGrace',
            sortable: false,
            width: '75px',
          },
        ] as TableHeader[]),
      );
    }

    headers.push(
      ...([
        {
          text: 'Demurrage Fuel Surcharge',
          align: 'left',
          value: 'demurrageFuelSurchargeApplies',
          sortable: false,
          width: '150px',
        },
        {
          text: 'Action',
          align: 'center',
          value: '',
          sortable: false,
          width: '75px',
        },
      ] as TableHeader[]),
    );

    return headers;
  }
  public serviceTypeName(serviceTypeId: number): string {
    const serviceType = getServiceTypeById(serviceTypeId);
    return serviceType ? serviceType.optionSelectName : '-';
  }
  get zoneRateItems(): RateTableItems[] {
    return this.serviceRate.rateTableItems.filter(
      (item: RateTableItems) => item.rateTypeId === this.rateId,
    );
  }

  get graceTimeInMinutes(): number {
    if (!this.zoneRate) {
      return 0;
    }
    return moment
      .duration(this.zoneRate.demurrage.graceTimeInMilliseconds)
      .asMinutes();
  }

  set graceTimeInMinutes(value: number) {
    if (!this.zoneRate) {
      return;
    }
    this.zoneRate.demurrage.graceTimeInMilliseconds = moment
      .duration(value, 'minutes')
      .asMilliseconds();
  }

  public saveZoneRate() {
    if (this.$refs.zoneForm && !this.$refs.zoneForm.validate()) {
      showNotification(FORM_VALIDATION_FAILED_MESSAGE);
      return;
    }

    if (
      ((this.editedServiceTypeId === -1 || this.editedZoneIndex === -1) &&
        !this.isNewRate) ||
      !this.zoneRate
    ) {
      if (this.openDialogForEdit) {
        showNotification(GENERIC_ERROR_MESSAGE);
      }
      return;
    }
    // when selectedServiceTypeId === -1 it means we need to update an existing zone rate else we push in a the new zone rate
    const serviceTypeId = !this.isNewRate
      ? this.editedServiceTypeId
      : this.selectedServiceTypeId;
    const zoneByService: RateTableItems | undefined =
      this.serviceRate.rateTableItems.find(
        (x: RateTableItems) =>
          x.serviceTypeId === serviceTypeId && x.rateTypeId === this.rateId,
      );

    if (!zoneByService) {
      return;
    }

    if (!this.isNewRate) {
      (zoneByService.rateTypeObject as ZoneRateType[]).splice(
        this.editedZoneIndex,
        1,
        this.zoneRate,
      );
    } else {
      // we should find the a new id to add to this.zoneRate.zone
      const zoneIds = (zoneByService.rateTypeObject as ZoneRateType[]).map(
        (x: ZoneRateType) => x.zone,
      );
      this.zoneRate.zone = zoneIds.length > 0 ? Math.max(...zoneIds) + 1 : 1;

      (zoneByService.rateTypeObject as ZoneRateType[]).push(this.zoneRate);
    }

    if (this.openDialogForEdit) {
      showNotification('Please remember to save your changes.', {
        type: HealthLevel.INFO,
      });
      this.cancelZoneEdit();
    }
  }

  public cancelZoneEdit() {
    this.selectedServiceTypeId = -1;
    this.editedServiceTypeId = -1;
    this.editedZoneIndex = -1;
    this.isNewRate = false;
    this.zoneRate = null;
    this.copyActive = false;
    this.copiedServiceTypeId = -1;
    this.selectedBulkChanges = [];
    this.openDialogForEdit = false;
  }

  public copyValue(serviceTypeId: number) {
    this.copyActive = true;
    this.copiedServiceTypeId = serviceTypeId;
  }

  public async bulkChanges() {
    // Find the source service by serviceTypeId
    const sourceServiceIndex = this.zoneRateItems.findIndex(
      (service) => service.serviceTypeId === this.copiedServiceTypeId,
    );

    if (sourceServiceIndex === -1) {
      return;
    }

    for (const targetServiceTypeId of this.selectedBulkChanges) {
      const targetServiceIndex = this.zoneRateItems.findIndex(
        (service) => service.serviceTypeId === targetServiceTypeId,
      );

      if (
        targetServiceIndex !== -1 &&
        targetServiceIndex !== sourceServiceIndex
      ) {
        const masterChange = this.zoneRateItems[sourceServiceIndex];
        const sourceZones: ZoneRateType[] =
          masterChange.rateTypeObject as ZoneRateType[];
        const targetZones: ZoneRateType[] = this.zoneRateItems[
          targetServiceIndex
        ].rateTypeObject as ZoneRateType[];

        //  confirmation dialog
        if (targetZones.length > 0 && targetZones[0].rate !== 0) {
          const ok = await requestUserConfirmation(
            'Are you sure you want to  overwrite the current zone rate?',
            'Copy Zone Rates',
          );

          if (!ok) {
            return;
          }
        }
        // Clear existing zones to avoid keeping old ones
        targetZones.length = 0;

        // Copy all zones from source to target
        for (const sourceZone of sourceZones) {
          const newZone = {
            zone: Math.max(...targetZones.map((z) => z.zone), 0) + 1,
            zoneName: sourceZone.zoneName,
            additionalDropOffFlagFall: sourceZone.additionalDropOffFlagFall,
            additionalPickUpFlagFall: sourceZone.additionalPickUpFlagFall,
            rate: sourceZone.rate,
            percentage: sourceZone.percentage,
            appliedFuelSurchargeId: sourceZone.appliedFuelSurchargeId,
            demurrage: {
              appliedDemurrageId: sourceZone.demurrage.appliedDemurrageId,
              rate: sourceZone.demurrage.rate,
              increment: sourceZone.demurrage.increment,
              incrementMultiplier: sourceZone.demurrage.incrementMultiplier,
              graceTimeInMilliseconds:
                sourceZone.demurrage.graceTimeInMilliseconds,
              demurrageFuelSurchargeApplies:
                sourceZone.demurrage.demurrageFuelSurchargeApplies,
            },
          };
          targetZones.push(newZone);
        }

        // Update the fuel surcharge as well
        this.zoneRateItems[targetServiceIndex].fuelSurcharge =
          masterChange.fuelSurcharge;
      }
    }

    this.selectedBulkChanges = [];
    this.copyActive = false;
    this.copiedServiceTypeId = -1;
  }

  public editZone(serviceTypeId: number, index: number) {
    this.editedServiceTypeId = serviceTypeId;
    this.editedZoneIndex = index;
    const p2pByService: RateTableItems | undefined = this.zoneRateItems.find(
      (x: RateTableItems) => x.serviceTypeId === this.editedServiceTypeId,
    );
    if (!p2pByService) {
      return;
    }
    this.zoneRate = JSON.parse(
      JSON.stringify(
        (p2pByService.rateTypeObject as ZoneRateType[])[this.editedZoneIndex],
      ),
    );
  }

  public generateNewZoneRate(serviceTypeId, index) {
    this.isNewRate = true;
    this.zoneRate = new ZoneRateType();
    this.editedZoneIndex = index;
    this.selectedServiceTypeId = serviceTypeId;
    this.editZone(serviceTypeId, index);

    // this.selectedServiceTypeId = this.existingServiceTypes[0].serviceTypeId;
    // this.editedServiceTypeId = this.selectedServiceTypeId;
    nextTick(() => {
      this.saveZoneRate();
    });
  }

  get existingServiceTypes() {
    const existingServiceTypeIds = this.zoneRateItems.map(
      (x: RateTableItems) => x.serviceTypeId,
    );
    return useCompanyDetailsStore().getServiceTypesList.filter(
      (service: ServiceTypes) =>
        existingServiceTypeIds.includes(service.serviceTypeId),
    );
  }

  get validate(): Validation {
    return validationRules;
  }

  public async removeRate(): Promise<void> {
    this.editedZoneIndex = -1;
    this.selectedServiceTypeId = -1;
    const ok = await requestUserConfirmation(
      'You are about to remove this zone rate. Do you wish to proceed?',
      'Zone Rate removal confirmation',
    );

    if (!ok) {
      return;
    }

    const zoneByService: RateTableItems | undefined =
      this.serviceRate.rateTableItems.find(
        (x: RateTableItems) =>
          x.serviceTypeId === this.editedServiceTypeId &&
          x.rateTypeId === this.rateId,
      );

    const serviceTypeName = this.serviceTypeName(this.editedServiceTypeId);

    if (!zoneByService) {
      return;
    }
    (zoneByService.rateTypeObject as ZoneRateType[]).splice(
      this.editedZoneIndex,
      1,
    );

    this.zoneRate = null;

    showNotification(
      serviceTypeName +
        ' zone rate successfully removed. Please Remember to save.',
      { type: HealthLevel.INFO },
    );
  }

  /**
   * Compute property for grouped zone rate table data with service headers
   */
  public getGroupedZoneRateTableData(): GroupedDataItem[] {
    if (!this.zoneRateItems?.length) {
      return [];
    }

    const groupedData: GroupedDataItem[] = [];

    this.zoneRateItems.forEach((service) => {
      const zoneRates: ZoneRateType[] =
        service.rateTypeObject as ZoneRateType[];
      const serviceName = this.serviceTypeName(service.serviceTypeId ?? 0);

      // Add service header row (no filler fields)
      groupedData.push({
        isServiceHeader: true,
        serviceTypeName: serviceName,
        serviceTypeId: service.serviceTypeId ?? null,
        rateTableItem: service,
      });

      // Add zone child rows
      zoneRates.forEach((zone: ZoneRateType, index: number) => {
        const appliedFuelSurcharge = this.applicableFuelSurcharges.find(
          (x: ShortLongName) => zone.appliedFuelSurchargeId === x.id,
        );

        const demurrageGraceAsMinutes = moment
          .duration(zone.demurrage.graceTimeInMilliseconds)
          .asMinutes();

        const appliedDemurrage = this.applicableDemurrages.find(
          (x: ShortLongName) => x.id === zone.demurrage.appliedDemurrageId,
        );

        const multiplier = rateMultipliers.find(
          (rateMultiplier) =>
            rateMultiplier.id === zone.demurrage.incrementMultiplier,
        );

        groupedData.push({
          isServiceHeader: false,
          isRangeRow: true,
          serviceTypeName: zone.zoneName,
          serviceTypeId: service.serviceTypeId ?? null,
          index,
          rate: zone.rate,
          zoneRate: '$' + DisplayCurrencyValue(zone.rate),
          additionalPickUpFlagFall: zone.additionalPickUpFlagFall,
          pickupFlagfall: DisplayCurrencyValue(zone.additionalPickUpFlagFall),
          additionalDropOffFlagFall: zone.additionalDropOffFlagFall,
          dropoffFlagfall: DisplayCurrencyValue(zone.additionalDropOffFlagFall),
          demurrage: zone.demurrage.rate,
          calculation: `${zone.percentage}%`,
          appliedFuelSurcharge: appliedFuelSurcharge
            ? appliedFuelSurcharge.shortName
            : '-',
          appliedDemurrageCharge: appliedDemurrage
            ? appliedDemurrage.shortName
            : '-',
          demurrageGrace: !demurrageGraceAsMinutes
            ? 'None'
            : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
          demurrageRate:
            '$' +
            DisplayCurrencyValue(zone.demurrage.rate) +
            '/' +
            zone.demurrage.increment +
            (multiplier ? multiplier.shortName : '-'),
          isClient: !this.isFleetAsset,
          demurrageFuelSurchargeApplies: zone.demurrage
            .demurrageFuelSurchargeApplies
            ? 'Apply'
            : "Don't Apply",
          rateTableItem: service,
        });
      });
    });

    return groupedData;
  }

  public isRateItemEmpty(rateItem): boolean {
    if (rateItem.isClient) {
      return rateItem.rate === 0;
    } else {
      return rateItem.calculation === '0%';
    }
  }

  // @Watch('isEdited')
  // public isEditedChange(val: boolean) {
  //   if (!this.isEdited) {
  //     this.cancelZoneEdit();
  //   }
  // }

  @Watch('zoneRate', { deep: true })
  public onZoneRateChange(val: ZoneRateType, newVal: ZoneRateType) {
    if (val && newVal && val !== newVal) {
      this.saveZoneRate();
    }
  }
}
